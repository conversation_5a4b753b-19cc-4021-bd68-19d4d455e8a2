import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { Globals } from '../_globals/endpoints.global';

// Interfaces for API payloads
export interface CreateIssuePayload {
  title: string;
  description: string;
  supplier_project_id: string;
  labels?: string[];
  assignee_ids?: number[];
  milestone_id?: number;
  confidential?: boolean;
  created_at?: string;
  due_date?: string;
  merge_request_to_resolve_discussions_of?: number;
  discussion_to_resolve?: string;
  weight?: number;
  epic_id?: number;
  epic_iid?: number;
}

export interface UpdateIssuePayload {
  title?: string;
  description?: string;
  state_event?: 'close' | 'reopen';
  labels?: string[];
  assignee_ids?: number[];
  milestone_id?: number;
  confidential?: boolean;
  due_date?: string;
  weight?: number;
  epic_id?: number;
  epic_iid?: number;
}

export interface CreateCommentPayload {
  body: string;
  confidential?: boolean;
}

export interface UpdateCommentPayload {
  body: string;
  confidential?: boolean;
}

export interface CreateDiscussionPayload {
  body: string;
  position?: {
    base_sha: string;
    start_sha: string;
    head_sha: string;
    position_type: string;
    new_path?: string;
    new_line?: number;
    old_path?: string;
    old_line?: number;
  };
}

export interface AddCommentToDiscussionPayload {
  body: string;
  position?: {
    base_sha: string;
    start_sha: string;
    head_sha: string;
    position_type: string;
    new_path?: string;
    new_line?: number;
    old_path?: string;
    old_line?: number;
  };
}

export interface IssueListParams {
  state?: 'opened' | 'closed' | 'all';
  labels?: string;
  milestone?: string;
  iids?: string;
  author_id?: number;
  assignee_id?: number;
  scope?: 'created_by_me' | 'assigned_to_me' | 'all';
  search?: string;
  in?: string;
  created_after?: string;
  created_before?: string;
  updated_after?: string;
  updated_before?: string;
  confidential?: boolean;
  order_by?: 'created_at' | 'updated_at' | 'priority' | 'due_date' | 'relative_position' | 'label_priority' | 'milestone_due' | 'popularity' | 'weight';
  sort?: 'asc' | 'desc';
  per_page?: number;
  page?: number;
}

export interface ProjectListParams {
  order_by?: 'id' | 'name' | 'path' | 'created_at' | 'updated_at' | 'last_activity_at';
  sort?: 'asc' | 'desc';
  archived?: boolean;
  visibility?: 'private' | 'internal' | 'public';
  search?: string;
  simple?: boolean;
  owned?: boolean;
  membership?: boolean;
  starred?: boolean;
  statistics?: boolean;
  with_issues_enabled?: boolean;
  with_merge_requests_enabled?: boolean;
  with_shared?: boolean;
  include_subgroups?: boolean;
  min_access_level?: number;
  per_page?: number;
  page?: number;
}

export interface GroupListParams {
  skip_groups?: string;
  all_available?: boolean;
  search?: string;
  order_by?: 'name' | 'path' | 'id' | 'similarity' | 'created_at' | 'updated_at';
  sort?: 'asc' | 'desc';
  statistics?: boolean;
  owned?: boolean;
  min_access_level?: number;
  top_level_only?: boolean;
  custom_attributes?: string;
  with_projects?: boolean;
  per_page?: number;
  page?: number;
}

@Injectable({
  providedIn: 'root',
})
export class DiscussionCommentsService {
  constructor(private http: HttpClient, private globals: Globals) {}

  // ===== ISSUES MANAGEMENT =====

  /**
   * Create a new issue
   * POST /api/subscriptions/{subscription_id}/tickets/projects/{project_id}/issues
   */
  createIssue(
    subscriptionId: string,
    projectId: string,
    payload: CreateIssuePayload
  ): Observable<any> {
    const url = this.globals.urlJoinWithTwoParam('tickets', 'createIssue', subscriptionId, projectId);
    return this.http.post(url, payload).pipe(
      catchError((err) => throwError(err))
    );
  }

  /**
   * Update an existing issue
   * PUT /api/subscriptions/{subscription_id}/tickets/projects/{project_id}/issues/{issue_iid}
   */
  updateIssue(
    subscriptionId: string,
    projectId: string,
    issueIid: number,
    payload: UpdateIssuePayload
  ): Observable<any> {
    const url = `${this.globals.urlJoinWithTwoParam('tickets', 'updateIssue', subscriptionId, projectId)}${issueIid}`;
    return this.http.put(url, payload).pipe(
      catchError((err) => throwError(err))
    );
  }

  /**
   * Get issue details
   * GET /api/subscriptions/{subscription_id}/tickets/projects/{project_id}/issues/{issue_iid}
   */
  getIssueDetails(
    subscriptionId: string,
    projectId: string,
    issueIid: number
  ): Observable<any> {
    const url = `${this.globals.urlJoinWithTwoParam('tickets', 'getIssueDetails', subscriptionId, projectId)}${issueIid}`;
    return this.http.get(url).pipe(
      catchError((err) => throwError(err))
    );
  }

  /**
   * List issues by group
   * GET /api/subscriptions/{subscription_id}/tickets/groups/{group_id}/issues
   */
  listIssuesByGroup(
    subscriptionId: string,
    groupId: string,
    params?: IssueListParams
  ): Observable<any> {
    let url = this.globals.urlJoinWithTwoParam('tickets', 'listIssuesByGroup', subscriptionId, groupId);

    if (params) {
      const httpParams = new HttpParams();
      Object.keys(params).forEach(key => {
        if (params[key] !== undefined && params[key] !== null) {
          httpParams.set(key, params[key].toString());
        }
      });
      url += `?${httpParams.toString()}`;
    }

    return this.http.get(url).pipe(
      catchError((err) => throwError(err))
    );
  }

  /**
   * List issues by project
   * GET /api/subscriptions/{subscription_id}/tickets/projects/{project_id}/issues/list
   */
  listIssuesByProject(
    subscriptionId: string,
    projectId: string,
    params?: IssueListParams
  ): Observable<any> {
    let url = this.globals.urlJoinWithTwoParam('tickets', 'listIssuesByProject', subscriptionId, projectId);

    if (params) {
      const httpParams = new HttpParams();
      Object.keys(params).forEach(key => {
        if (params[key] !== undefined && params[key] !== null) {
          httpParams.set(key, params[key].toString());
        }
      });
      url += `?${httpParams.toString()}`;
    }

    return this.http.get(url).pipe(
      catchError((err) => throwError(err))
    );
  }

  // ===== COMMENTS MANAGEMENT =====

  /**
   * Create a comment on an issue
   * POST /api/subscriptions/{subscription_id}/tickets/projects/{project_id}/issues/{issue_iid}/notes
   */
  createComment(
    subscriptionId: string,
    projectId: string,
    issueIid: number,
    payload: CreateCommentPayload
  ): Observable<any> {
    const url = `${this.globals.urlJoinWithTwoParam('tickets', 'createComment', subscriptionId, projectId)}${issueIid}/notes`;
    return this.http.post(url, payload).pipe(
      catchError((err) => throwError(err))
    );
  }

  /**
   * Edit a comment on an issue
   * PUT /api/subscriptions/{subscription_id}/tickets/projects/{project_id}/issues/{issue_iid}/notes/{note_id}
   */
  editComment(
    subscriptionId: string,
    projectId: string,
    issueIid: number,
    noteId: number,
    payload: UpdateCommentPayload
  ): Observable<any> {
    const url = `${this.globals.urlJoinWithTwoParam('tickets', 'editComment', subscriptionId, projectId)}${issueIid}/notes/${noteId}`;
    return this.http.put(url, payload).pipe(
      catchError((err) => throwError(err))
    );
  }

  /**
   * Delete a comment on an issue
   * DELETE /api/subscriptions/{subscription_id}/tickets/projects/{project_id}/issues/{issue_iid}/notes/{note_id}
   */
  deleteComment(
    subscriptionId: string,
    projectId: string,
    issueIid: number,
    noteId: number
  ): Observable<any> {
    const url = `${this.globals.urlJoinWithTwoParam('tickets', 'deleteComment', subscriptionId, projectId)}${issueIid}/notes/${noteId}`;
    return this.http.delete(url).pipe(
      catchError((err) => throwError(err))
    );
  }

  // ===== DISCUSSIONS MANAGEMENT =====

  /**
   * List discussions for an issue
   * GET /api/subscriptions/{subscription_id}/tickets/projects/{project_id}/issues/{issue_iid}/discussions
   */
  listDiscussionsForIssue(
    subscriptionId: string,
    projectId: string,
    issueIid: number
  ): Observable<any> {
    const url = `${this.globals.urlJoinWithTwoParam('tickets', 'listDiscussionsForIssue', subscriptionId, projectId)}${issueIid}/discussions`;
    return this.http.get(url).pipe(
      catchError((err) => throwError(err))
    );
  }

  /**
   * Create a discussion on an issue
   * POST /api/subscriptions/{subscription_id}/tickets/projects/{project_id}/issues/{issue_iid}/discussions/thread
   */
  createDiscussion(
    subscriptionId: string,
    projectId: string,
    issueIid: number,
    payload: CreateDiscussionPayload
  ): Observable<any> {
    const url = `${this.globals.urlJoinWithTwoParam('tickets', 'createDiscussion', subscriptionId, projectId)}${issueIid}/discussions/thread`;
    return this.http.post(url, payload).pipe(
      catchError((err) => throwError(err))
    );
  }

  /**
   * Add a comment to a discussion
   * POST /api/subscriptions/{subscription_id}/tickets/projects/{project_id}/issues/{issue_iid}/discussions/{discussion_id}/notes
   */
  addCommentToDiscussion(
    subscriptionId: string,
    projectId: string,
    issueIid: number,
    discussionId: string,
    payload: AddCommentToDiscussionPayload
  ): Observable<any> {
    const url = `${this.globals.urlJoinWithTwoParam('tickets', 'addCommentToDiscussion', subscriptionId, projectId)}${issueIid}/discussions/${discussionId}/notes`;
    return this.http.post(url, payload).pipe(
      catchError((err) => throwError(err))
    );
  }

  /**
   * Update a comment in a discussion
   * PUT /api/subscriptions/{subscription_id}/tickets/projects/{project_id}/issues/{issue_iid}/discussions/{discussion_id}/notes/{note_id}
   */
  updateCommentInDiscussion(
    subscriptionId: string,
    projectId: string,
    issueIid: number,
    discussionId: string,
    noteId: number,
    payload: UpdateCommentPayload
  ): Observable<any> {
    const url = `${this.globals.urlJoinWithTwoParam('tickets', 'updateCommentInDiscussion', subscriptionId, projectId)}${issueIid}/discussions/${discussionId}/notes/${noteId}`;
    return this.http.put(url, payload).pipe(
      catchError((err) => throwError(err))
    );
  }

  /**
   * Delete a comment in a discussion
   * DELETE /api/subscriptions/{subscription_id}/tickets/projects/{project_id}/issues/{issue_iid}/discussions/{discussion_id}/notes/{note_id}
   */
  deleteCommentInDiscussion(
    subscriptionId: string,
    projectId: string,
    issueIid: number,
    discussionId: string,
    noteId: number
  ): Observable<any> {
    const url = `${this.globals.urlJoinWithTwoParam('tickets', 'deleteCommentInDiscussion', subscriptionId, projectId)}${issueIid}/discussions/${discussionId}/notes/${noteId}`;
    return this.http.delete(url).pipe(
      catchError((err) => throwError(err))
    );
  }

  // ===== PROJECTS AND GROUPS MANAGEMENT =====

  /**
   * List projects by group
   * GET /api/subscriptions/{subscription_id}/tickets/groups/{group_id}/projects
   */
  listProjectsByGroup(
    subscriptionId: string,
    groupId: string,
    params?: ProjectListParams
  ): Observable<any> {
    let url = this.globals.urlJoinWithTwoParam('tickets', 'listProjectsByGroup', subscriptionId, groupId);

    if (params) {
      const httpParams = new HttpParams();
      Object.keys(params).forEach(key => {
        if (params[key] !== undefined && params[key] !== null) {
          httpParams.set(key, params[key].toString());
        }
      });
      url += `?${httpParams.toString()}`;
    }

    return this.http.get(url).pipe(
      catchError((err) => throwError(err))
    );
  }

  /**
   * List groups
   * GET /api/subscriptions/{subscription_id}/tickets/groups
   */
  listGroups(
    subscriptionId: string,
    params?: GroupListParams
  ): Observable<any> {
    let url = this.globals.urlJoinWithParam('tickets', 'listGroups', subscriptionId);

    if (params) {
      const httpParams = new HttpParams();
      Object.keys(params).forEach(key => {
        if (params[key] !== undefined && params[key] !== null) {
          httpParams.set(key, params[key].toString());
        }
      });
      url += `?${httpParams.toString()}`;
    }

    return this.http.get(url).pipe(
      catchError((err) => throwError(err))
    );
  }

  // ===== LEGACY METHODS (keeping for backward compatibility) =====

  /**
   * Add a comment to a discussion (legacy method - for backward compatibility)
   */
  addComment(
    subscriptionId: string,
    projectId: string,
    issueIid: number,
    discussionId: string,
    body: string,
  ): Observable<any> {
    return this.addCommentToDiscussion(subscriptionId, projectId, issueIid, discussionId, { body });
  }

  /**
   * Update a comment in a discussion (legacy method - for backward compatibility)
   */
  updateCommentInDiscussionLegacy(
    subscriptionId: string,
    projectId: string,
    issueIid: number,
    discussionId: string,
    noteId: number,
    body: string,
  ): Observable<any> {
    return this.updateCommentInDiscussion(subscriptionId, projectId, issueIid, discussionId, noteId, { body });
  }

  /**
   * Delete a comment in a discussion (legacy method - for backward compatibility)
   */
  deleteCommentInDiscussionLegacy(
    subscriptionId: string,
    projectId: string,
    issueIid: number,
    discussionId: string,
    noteId: number,
  ): Observable<any> {
    return this.deleteCommentInDiscussion(subscriptionId, projectId, issueIid, discussionId, noteId);
  }

  /**
   * Get all tickets/issues for a project (legacy method - for backward compatibility)
   */
  getTickets(subscriptionId: string, projectId: string): Observable<any> {
    return this.listIssuesByProject(subscriptionId, projectId);
  }

  /**
   * Create a new ticket (legacy method - for backward compatibility)
   */
  createTicket(
    subscriptionId: string,
    projectId: string,
    payload: { title: string; description: string; supplier_project_id: string; }
  ): Observable<any> {
    return this.createIssue(subscriptionId, projectId, payload);
  }

  /**
   * Update an existing ticket (legacy method - for backward compatibility)
   */
  updateTicket(
    subscriptionId: string,
    projectId: string,
    issueIid: number,
    payload: { title?: string; description?: string; state_event?: 'close' | 'reopen' }
  ): Observable<any> {
    return this.updateIssue(subscriptionId, projectId, issueIid, payload);
  }

  // Get discussions for a ticket (legacy method - for backward compatibility)
  getDiscussions(subscriptionId: string, projectId: string, issueIid: number): Observable<any> {
    return this.listDiscussionsForIssue(subscriptionId, projectId, issueIid);
  }

  /**
   * Get ticket details (legacy method - for backward compatibility)
   */
  getTicketDetails(
    subscriptionId: string,
    projectId: string,
    issueIid: number
  ): Observable<any> {
    return this.getIssueDetails(subscriptionId, projectId, issueIid);
  }

  // Get notes (comments) for a discussion (legacy method)
  getNotes(subscriptionId: string, projectId: string, issueIid: number, discussionId: string): Observable<any> {
    const url = `${this.globals.urlJoinWithTwoParam('tickets', 'getIssueDetails', subscriptionId, projectId)}${issueIid}/discussions/${discussionId}/notes`;
    return this.http.get(url).pipe(catchError((err) => throwError(err)));
  }

  /**
   * Add a note directly to an issue (first comment, no discussionId)
   */
  addNoteToIssue(
    subscriptionId: string,
    projectId: string,
    issueIid: number,
    body: string,
  ): Observable<any> {
    return this.createComment(subscriptionId, projectId, issueIid, { body });
  }

  /**
   * Update a top-level note (not in a discussion)
   */
  updateNoteOnIssue(
    subscriptionId: string,
    projectId: string,
    issueIid: number,
    noteId: number,
    body: string,
  ): Observable<any> {
    return this.editComment(subscriptionId, projectId, issueIid, noteId, { body });
  }

  /**
   * Delete a top-level note (not in a discussion)
   */
  deleteNoteOnIssue(
    subscriptionId: string,
    projectId: string,
    issueIid: number,
    noteId: number,
  ): Observable<any> {
    return this.deleteComment(subscriptionId, projectId, issueIid, noteId);
  }

  // ===== UTILITY METHODS =====

  /**
   * Get all tickets for a project with default parameters
   */
  getTicketsForProject(
    subscriptionId: string,
    projectId: string,
    state: 'opened' | 'closed' | 'all' = 'opened'
  ): Observable<any> {
    return this.listIssuesByProject(subscriptionId, projectId, { state });
  }

  /**
   * Get all tickets for a group with default parameters
   */
  getTicketsForGroup(
    subscriptionId: string,
    groupId: string,
    state: 'opened' | 'closed' | 'all' = 'opened'
  ): Observable<any> {
    return this.listIssuesByGroup(subscriptionId, groupId, { state });
  }

  /**
   * Get all discussions and comments for an issue
   */
  getIssueWithDiscussions(
    subscriptionId: string,
    projectId: string,
    issueIid: number
  ): Observable<any> {
    return this.getIssueDetails(subscriptionId, projectId, issueIid).pipe(
      map(issue => {
        // The issue details should include discussions
        return issue;
      })
    );
  }

  /**
   * Create a simple issue with minimal required fields
   */
  createSimpleIssue(
    subscriptionId: string,
    projectId: string,
    title: string,
    description: string,
    supplierProjectId: string
  ): Observable<any> {
    const payload: CreateIssuePayload = {
      title,
      description,
      supplier_project_id: supplierProjectId
    };
    return this.createIssue(subscriptionId, projectId, payload);
  }

  /**
   * Close an issue
   */
  closeIssue(
    subscriptionId: string,
    projectId: string,
    issueIid: number
  ): Observable<any> {
    const payload: UpdateIssuePayload = {
      state_event: 'close'
    };
    return this.updateIssue(subscriptionId, projectId, issueIid, payload);
  }

  /**
   * Reopen an issue
   */
  reopenIssue(
    subscriptionId: string,
    projectId: string,
    issueIid: number
  ): Observable<any> {
    const payload: UpdateIssuePayload = {
      state_event: 'reopen'
    };
    return this.updateIssue(subscriptionId, projectId, issueIid, payload);
  }

  // ===== GITLAB-LIKE LABELS & MEMBERS API INTEGRATION =====

  /**
   * Get project labels
   * GET /api/subscriptions/rs2a/:subscription_id/tickets/projects/:project_id/labels
   */
  getProjectLabels(subscriptionId: string, projectId: string, privateToken?: string): Observable<any> {
    const url = this.globals.urlJoinWithTwoParam('tickets', 'projectLabels', subscriptionId, projectId);
    const options = privateToken ? { headers: new HttpHeaders({ 'PRIVATE-TOKEN': privateToken }) } : {};
    return this.http.get(url, options).pipe(catchError((err) => throwError(err)));
  }

  /**
   * Get group labels
   * GET /api/subscriptions/rs2a/:subscription_id/tickets/groups/:group_id/labels
   */
  getGroupLabels(subscriptionId: string, groupId: string, privateToken?: string): Observable<any> {
    const url = this.globals.urlJoinWithTwoParam('tickets', 'groupLabels', subscriptionId, groupId);
    const options = privateToken ? { headers: new HttpHeaders({ 'PRIVATE-TOKEN': privateToken }) } : {};
    return this.http.get(url, options).pipe(catchError((err) => throwError(err)));
  }

  /**
   * Get group members
   * GET /api/subscriptions/rs2a/:subscription_id/tickets/groups/:group_id/members/all
   */
  getGroupMembers(subscriptionId: string, groupId: string, privateToken?: string): Observable<any> {
    const url = this.globals.urlJoinWithTwoParam('tickets', 'groupMembers', subscriptionId, groupId);
    const options = privateToken ? { headers: new HttpHeaders({ 'PRIVATE-TOKEN': privateToken }) } : {};
    return this.http.get(url, options).pipe(catchError((err) => throwError(err)));
  }

  /**
   * Get project members
   * GET /api/subscriptions/rs2a/:subscription_id/tickets/projects/:project_id/members/all
   */
  getProjectMembers(subscriptionId: string, projectId: string, privateToken?: string): Observable<any> {
    const url = this.globals.urlJoinWithTwoParam('tickets', 'projectMembers', subscriptionId, projectId);
    const options = privateToken ? { headers: new HttpHeaders({ 'PRIVATE-TOKEN': privateToken }) } : {};
    return this.http.get(url, options).pipe(catchError((err) => throwError(err)));
  }
}
