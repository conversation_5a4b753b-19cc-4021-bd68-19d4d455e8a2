<div class="ticket-view-container">
  <div class="ticket-header-row">
    <div class="ticket-header-title">
      <span>Help-Center</span>
    </div>
    <button mat-raised-button color="primary" (click)="onRaiseTicket()">Raise Ticket</button>
  </div>

  <div class="ticket-tabs-row">
    <div
      *ngFor="let tab of tabs"
      class="ticket-tab"
      [class.active]="selectedTab.id === tab.id"
      (click)="selectTab(tab)"
    >
      {{ tab.header }}
      <span *ngIf="tab.id === 'all' && allTicketsCount > 0" class="tab-badge">{{ allTicketsCount }}</span>
      <span *ngIf="tab.id === 'my-tickets' && myTicketsCount > 0" class="tab-badge">{{ myTicketsCount }}</span>
    </div>
  </div>

  <div class="ticket-list">
    <div
      *ngFor="let ticket of filteredTickets"
      class="ticket-item"
      [class.selected]="selectedTicketId === ticket.id"
      [class.unread]="!ticket.read"
      (click)="onTicketClick(ticket)"
    >
      <div class="ticket-item-main">
        <div class="ticket-item-type-row">
          <span class="ticket-item-type">{{ ticket.title }}</span>
          <span class="ticket-item-badge">{{ ticket.ticketId }}</span>
          <span class="ticket-item-badge status-badge" [ngClass]="ticket.status">{{ ticket.status | titlecase }}</span>
        </div>
        <div class="ticket-item-message">{{ ticket.message }}</div>
      </div>
      <div class="ticket-item-meta">
        <span class="ticket-item-date">{{ ticket.date | date: 'MMM d' }}</span>
        <span class="ticket-item-user"><span class="by">-- by </span>{{ logDisplayAuthor(ticket) }}</span>
      </div>
    </div>
  </div>
</div>

<!-- Sidebar backdrop -->
<div *ngIf="sidebarOpen" class="sidebar-backdrop" [ngClass]="{'sidebar-open': sidebarOpen}" (click)="onCancel()"></div>

<div class="ticket-sidebar" *ngIf="sidebarOpen" [ngClass]="{'sidebar-open': sidebarOpen}">
  <!-- Header: Always visible -->
  <div class="sidebar-header">
    <div class="header-content">
      <h2 class="header-title">
        <span *ngIf="sidebarMode === 'edit'">Edit Ticket</span>
        <span *ngIf="sidebarMode === 'new'">Raise Ticket</span>
      </h2>
      <button class="header-close-btn" (click)="onCancel()">
        <mat-icon>close</mat-icon>
      </button>
    </div>
  </div>

  <!-- Main Content Area: Show spinner or hide while loading -->
  <div class="sidebar-content">
    <!-- Show group labels as chips -->
    <!-- <div *ngIf="groupLabels.length" class="group-labels-row" style="margin-bottom: 12px; display: flex; flex-wrap: wrap; gap: 8px;">
      <span *ngFor="let label of groupLabels" class="label-chip" [ngStyle]="{'background': label.color, 'color': label.text_color, 'padding': '4px 12px', 'border-radius': '12px', 'font-size': '13px', 'font-weight': 500}">
        {{ label.name }}
      </span>
    </div> -->
    <div *ngIf="isSubmitting || commentSectionLoading" class="sidebar-loading-spinner" style="display: flex; justify-content: center; align-items: center; min-height: 200px;">
      <mat-spinner diameter="48"></mat-spinner>
    </div>
    <ng-container *ngIf="!isSubmitting && !commentSectionLoading">
      <!-- Ticket Info Row (vertical, key-value, key bold/dark, value lighter) -->
      <div *ngIf="sidebarMode === 'edit' && selectedTicket" class="ticket-info-vertical">
        <div class="info-line"><span class="info-label">Ticket ID:</span> <span class="info-value">{{ selectedTicket.ticketId }}</span></div>
        <div class="info-line"><span class="info-label">Created by:</span> <span class="info-value">{{ selectedTicket.user }}</span></div>
        <div class="info-line"><span class="info-label">Date:</span> <span class="info-value">{{ selectedTicket.date | date: 'MMM d, y' }}</span></div>
        <div class="info-line">
          <span class="info-label">Status:</span>
          <span class="info-value status-badge" [ngClass]="selectedTicket.status">
            {{ selectedTicket.status | titlecase }}
          </span>
        </div>
      </div>
      <!-- Form Fields Section -->
      <div class="form-section">
        <div class="form-field-group">
          <label class="form-label">Title <span class="required">*</span></label>
          <input
            class="form-input"
            [class.error]="formErrors.title"
            type="text"
            [(ngModel)]="editTitle"
            (input)="onFieldChange('title')"
            [disabled]="sidebarMode === 'edit' && !isTicketOwner"
            placeholder="Enter ticket title" />
          <div *ngIf="formErrors.title" class="error-message">{{ formErrors.title }}</div>
        </div>

        <div class="form-field-group">
          <label class="form-label">Description <span class="required">*</span></label>
          <textarea
            class="form-textarea"
            [class.error]="formErrors.message"
            rows="2"
            [(ngModel)]="editMessage"
            (input)="onFieldChange('message')"
            [disabled]="sidebarMode === 'edit' && !isTicketOwner"
            placeholder="Describe your issue or request"></textarea>
          <div *ngIf="formErrors.message" class="error-message">{{ formErrors.message }}</div>
        </div>
      </div>
      <!-- Timeline Section -->
      <div class="timeline-section">
        <div class="timeline-header">
          <h3 class="timeline-title">Activity</h3>
        </div>

        <!-- GitLab-style Timeline -->
        <div class="gitlab-timeline">
          <ng-container *ngFor="let group of groupedComments; let groupIndex = index">
            <!-- Parent Comment Container -->
            <div class="timeline-item" #parentCard>
              <!-- Timeline Line -->
              <div class="timeline-line" *ngIf="groupIndex < groupedComments.length - 1"></div>

              <!-- Timeline Avatar -->
              <div class="timeline-avatar-wrapper">
                <div class="timeline-avatar" [ngStyle]="{'background': getAvatarColor(group.comments[0].userName || group.comments[0].user)}">
                  <ng-container *ngIf="group.comments[0].avatarUrl; else parentInitials">
                    <img [src]="group.comments[0].avatarUrl" alt="Avatar" />
                  </ng-container>
                  <ng-template #parentInitials>
                    <span>{{ getAvatarText(group.comments[0].userName || group.comments[0].user) }}</span>
                  </ng-template>
                </div>
              </div>

              <!-- Comment Content -->
              <div class="timeline-content">
                <!-- Parent Comment Card -->
                <div class="comment-card">
                  <!-- Comment Header -->
                  <div class="comment-header">
                    <div class="comment-author-info">
                      <span class="comment-author">{{ logDisplayAuthor(group.comments[0]) }}</span>
                      <span class="comment-timestamp">{{ group.comments[0].timestamp | timeAgo }}</span>
                    </div>
                    <div class="comment-actions">
                      <button mat-icon-button class="action-btn" (click)="startReply(group.comments[0])" title="Reply">
                        <mat-icon>reply</mat-icon>
                      </button>
                      <button mat-icon-button class="action-btn" (click)="startEditComment(group.comments[0])"
                              *ngIf="editingCommentId !== group.comments[0].id && canEditComment(group.comments[0])" title="Edit">
                        <mat-icon>edit</mat-icon>
                      </button>
                      <button mat-icon-button class="action-btn delete-btn" (click)="deleteComment(group.comments[0])"
                              *ngIf="canDeleteComment(group.comments[0])" title="Delete">
                        <mat-icon>delete</mat-icon>
                      </button>
                    </div>
                  </div>

                  <!-- Comment Body -->
                  <div class="comment-body" *ngIf="editingCommentId !== group.comments[0].id">
                    <div [innerHTML]="(group.comments[0].text || group.comments[0].body) | safe:'html'"></div>
                  </div>

                  <!-- Edit Mode for Parent Comment -->
                  <div class="comment-body" *ngIf="editingCommentId === group.comments[0].id">
                    <div class="comment-edit-container">
                      <quill-editor
                        [(ngModel)]="editingCommentContent"
                        [modules]="quillModules"
                        placeholder="Edit your comment..."
                        class="modern-quill-editor edit-mode">
                      </quill-editor>
                      <div class="edit-actions">
                        <button mat-button color="primary" (click)="saveEditedComment(group.comments[0])"
                                [disabled]="!editingCommentContent.trim() || isCommentSubmitting" [class.loading]="isCommentSubmitting">
                          <mat-icon>save</mat-icon>
                          {{ isCommentSubmitting ? 'Saving...' : 'Save' }}
                        </button>
                        <button mat-button (click)="cancelEditComment()" [disabled]="isCommentSubmitting">
                          <mat-icon>cancel</mat-icon>
                          Cancel
                        </button>
                      </div>
                    </div>
                  </div>

                  <!-- Replies Section -->
                  <div class="replies-section" *ngIf="group.isDiscussion && group.comments.length > 1">
                    <!-- Collapse/Expand Button -->
                    <div class="replies-toggle" *ngIf="group.comments.length > 1">
                      <button class="toggle-replies-btn" (click)="toggleGroupCollapse(group)">
                        <mat-icon>{{ isGroupCollapsed(group) ? 'expand_more' : 'expand_less' }}</mat-icon>
                        <span>{{ isGroupCollapsed(group) ? 'Show' : 'Hide' }} {{ group.comments.length - 1 }} {{ (group.comments.length - 1) === 1 ? 'reply' : 'replies' }}</span>
                      </button>
                    </div>

                    <!-- Replies List -->
                    <div class="replies-list" *ngIf="!isGroupCollapsed(group)">
                      <ng-container *ngFor="let reply of group.comments.slice(1); let replyIndex = index">
                        <div class="reply-item" #lastCommentDiv *ngIf="groupIndex === groupedComments.length - 1 && replyIndex === group.comments.length - 2">
                          <!-- Reply Avatar -->
                          <div class="reply-avatar">
                            <div class="avatar-circle" [ngStyle]="{'background': getAvatarColor(reply.userName || reply.user)}">
                              <ng-container *ngIf="reply.avatarUrl; else replyInitials">
                                <img [src]="reply.avatarUrl" alt="Avatar" />
                              </ng-container>
                              <ng-template #replyInitials>
                                <span>{{ getAvatarText(reply.userName || reply.user) }}</span>
                              </ng-template>
                            </div>
                          </div>

                          <!-- Reply Content -->
                          <div class="reply-content">
                            <!-- Reply Header -->
                            <div class="reply-header">
                              <div class="reply-author-info">
                                <span class="reply-author">{{ logDisplayAuthor(reply) }}</span>
                                <span class="reply-timestamp">{{ reply.timestamp | timeAgo }}</span>
                              </div>
                              <div class="reply-actions">
                                <button mat-icon-button class="action-btn" (click)="startEditComment(reply)"
                                        *ngIf="editingCommentId !== reply.id && canEditComment(reply)" title="Edit">
                                  <mat-icon>edit</mat-icon>
                                </button>
                                <button mat-icon-button class="action-btn delete-btn" (click)="deleteComment(reply)"
                                        *ngIf="canDeleteComment(reply)" title="Delete">
                                  <mat-icon>delete</mat-icon>
                                </button>
                              </div>
                            </div>

                            <!-- Reply Body -->
                            <div class="reply-body" *ngIf="editingCommentId !== reply.id">
                              <div [innerHTML]="(reply.text || reply.body) | safe:'html'"></div>
                            </div>

                            <!-- Edit Mode for Reply -->
                            <div class="reply-body" *ngIf="editingCommentId === reply.id">
                              <div class="comment-edit-container">
                                <quill-editor
                                  [(ngModel)]="editingCommentContent"
                                  [modules]="quillModules"
                                  placeholder="Edit your reply..."
                                  class="modern-quill-editor edit-mode">
                                </quill-editor>
                                <div class="edit-actions">
                                  <button mat-button color="primary" (click)="saveEditedComment(reply)"
                                          [disabled]="!editingCommentContent.trim() || isCommentSubmitting" [class.loading]="isCommentSubmitting">
                                    <mat-icon>save</mat-icon>
                                    {{ isCommentSubmitting ? 'Saving...' : 'Save' }}
                                  </button>
                                  <button mat-button (click)="cancelEditComment()" [disabled]="isCommentSubmitting">
                                    <mat-icon>cancel</mat-icon>
                                    Cancel
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- Regular reply item (not the last one) -->
                        <div class="reply-item" *ngIf="!(groupIndex === groupedComments.length - 1 && replyIndex === group.comments.length - 2)">
                          <!-- Reply Avatar -->
                          <div class="reply-avatar">
                            <div class="avatar-circle" [ngStyle]="{'background': getAvatarColor(reply.userName || reply.user)}">
                              <ng-container *ngIf="reply.avatarUrl; else replyInitials2">
                                <img [src]="reply.avatarUrl" alt="Avatar" />
                              </ng-container>
                              <ng-template #replyInitials2>
                                <span>{{ getAvatarText(reply.userName || reply.user) }}</span>
                              </ng-template>
                            </div>
                          </div>

                          <!-- Reply Content -->
                          <div class="reply-content">
                            <!-- Reply Header -->
                            <div class="reply-header">
                              <div class="reply-author-info">
                                <span class="reply-author">{{ logDisplayAuthor(reply) }}</span>
                                <span class="reply-timestamp">{{ reply.timestamp | timeAgo }}</span>
                              </div>
                              <div class="reply-actions">
                                <button mat-icon-button class="action-btn" (click)="startEditComment(reply)"
                                        *ngIf="editingCommentId !== reply.id && canEditComment(reply)" title="Edit">
                                  <mat-icon>edit</mat-icon>
                                </button>
                                <button mat-icon-button class="action-btn delete-btn" (click)="deleteComment(reply)"
                                        *ngIf="canDeleteComment(reply)" title="Delete">
                                  <mat-icon>delete</mat-icon>
                                </button>
                              </div>
                            </div>

                            <!-- Reply Body -->
                            <div class="reply-body" *ngIf="editingCommentId !== reply.id">
                              <div [innerHTML]="(reply.text || reply.body) | safe:'html'"></div>
                            </div>

                            <!-- Edit Mode for Reply -->
                            <div class="reply-body" *ngIf="editingCommentId === reply.id">
                              <div class="comment-edit-container">
                                <quill-editor
                                  [(ngModel)]="editingCommentContent"
                                  [modules]="quillModules"
                                  placeholder="Edit your reply..."
                                  class="modern-quill-editor edit-mode">
                                </quill-editor>
                                <div class="edit-actions">
                                  <button mat-button color="primary" (click)="saveEditedComment(reply)"
                                          [disabled]="!editingCommentContent.trim() || isCommentSubmitting" [class.loading]="isCommentSubmitting">
                                    <mat-icon>save</mat-icon>
                                    {{ isCommentSubmitting ? 'Saving...' : 'Save' }}
                                  </button>
                                  <button mat-button (click)="cancelEditComment()" [disabled]="isCommentSubmitting">
                                    <mat-icon>cancel</mat-icon>
                                    Cancel
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </ng-container>
                    </div>

                    <!-- Reply Input -->
                    <div class="reply-input-section">
                      <div class="reply-input-wrapper">
                        <input class="reply-input" placeholder="Write a reply..." [(ngModel)]="replyContent[group.comments[0].id]" />
                        <div class="reply-input-actions">
                          <button mat-button color="primary" class="reply-btn" (click)="sendReply(group.comments[0])"
                                  [disabled]="!replyContent[group.comments[0].id] || isCommentSubmitting">
                            Reply
                          </button>
                          <button mat-button class="cancel-btn" (click)="cancelReply()" [disabled]="isCommentSubmitting">
                            Cancel
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ng-container>
        </div>
      </div>
    </ng-container>
  </div>


  <!-- Comment Input Section: Always visible -->
  <div class="comment-input-section">
    <div class="comment-input-container" [class.disabled]="isCommentSubmitting || !isCommentEditorEnabled">
      <div *ngIf="isCommentSubmitting" class="comment-loading-spinner" style="display: flex; justify-content: center; align-items: center; min-height: 40px;">
        <mat-spinner diameter="24"></mat-spinner>
      </div>
      <quill-editor
        [(ngModel)]="editorContent"
        [modules]="quillModules"
        (onEditorCreated)="onEditorCreated($event)"
        [placeholder]="sidebarMode === 'new' && !isCommentEditorEnabled ? 'Please fill in the title first...' : 'Leave a comment... Use @ to mention users...'"
        [disabled]="!isCommentEditorEnabled || isCommentSubmitting"
        class="modern-quill-editor">
      </quill-editor>

      <div class="pending-file-list" *ngIf="pendingFiles.length">
        <div class="pending-file-item" *ngFor="let file of pendingFiles; let i = index">
          <mat-icon *ngIf="isImage(file.name)">image</mat-icon>
          <mat-icon *ngIf="!isImage(file.name)">insert_drive_file</mat-icon>
          <span class="pending-file-name">{{ file.name }}</span>
          <button mat-icon-button color="warn" (click)="pendingFiles.splice(i, 1)">
            <mat-icon>close</mat-icon>
          </button>
        </div>
      </div>
      <div class="comment-actions" style="position:absolute; bottom:12px; right:12px;">
        <button
          class="comment-send-btn"
          [disabled]="!editorContent || !isCommentEditorEnabled || isCommentSubmitting"
          [class.disabled]="!editorContent || !isCommentEditorEnabled || isCommentSubmitting"
          (click)="sendComment()">
          <mat-icon>send</mat-icon>
        </button>
      </div>
    </div>
  </div>

  <!-- Footer: Always visible -->
  <div class="sidebar-footer">
    <button class="btn-secondary" (click)="onCancel()" [disabled]="isSubmitting">Cancel</button>
    <button
      class="btn-primary"
      (click)="saveTicket()"
      [disabled]="(sidebarMode === 'edit' && !isTicketOwner) || isSubmitting">
      <mat-icon *ngIf="isSubmitting" class="spinner">hourglass_empty</mat-icon>
      {{ isSubmitting ? 'Saving...' : 'Save Changes' }}
    </button>
  </div>
</div>

